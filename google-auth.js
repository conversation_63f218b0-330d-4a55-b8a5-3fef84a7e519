/**
 * Google Authentication Service for CalenTask Chrome Extension
 * Handles OAuth 2.0 authentication with Google APIs
 */
class GoogleAuthService {
  constructor() {
    this.isAuthenticated = false;
    this.accessToken = null;
    this.refreshToken = null;
    this.tokenExpiry = null;
    this.userInfo = null;
    
    // Initialize authentication state from storage
    this.loadAuthState();
  }

  /**
   * Load authentication state from Chrome storage
   */
  async loadAuthState() {
    try {
      const result = await chrome.storage.local.get([
        'google_access_token',
        'google_refresh_token', 
        'google_token_expiry',
        'google_user_info',
        'google_auth_enabled'
      ]);

      this.accessToken = result.google_access_token || null;
      this.refreshToken = result.google_refresh_token || null;
      this.tokenExpiry = result.google_token_expiry || null;
      this.userInfo = result.google_user_info || null;
      
      // Only set authenticated if we have both the flag and a token
      const hasValidAuth = result.google_auth_enabled && result.google_access_token;
      this.isAuthenticated = hasValidAuth || false;

      // Check if we have authentication data but token is expired
      if (this.isAuthenticated && this.accessToken && this.isTokenExpired()) {
        console.log('Token expired, attempting to refresh...');
        const refreshed = await this.refreshAccessToken();
        if (!refreshed) {
          console.warn('Failed to refresh token on load, clearing auth state');
          await this.clearAuthState();
        } else {
          console.log('Token refreshed successfully');
        }
      }
      
      // Dispatch an event to notify the app that auth state has been loaded
      this._dispatchAuthStateChangedEvent();
      
      return this.isAuthenticated;
    } catch (error) {
      console.error('Error loading auth state:', error);
      await this.clearAuthState();
      return false;
    }
  }

  /**
   * Save authentication state to Chrome storage
   */
  async saveAuthState() {
    try {
      await chrome.storage.local.set({
        google_access_token: this.accessToken,
        google_refresh_token: this.refreshToken,
        google_token_expiry: this.tokenExpiry,
        google_user_info: this.userInfo,
        google_auth_enabled: this.isAuthenticated
      });
      
      // Notify listeners that auth state has been saved
      this._dispatchAuthStateChangedEvent();
      console.log('Auth state saved successfully');
      return true;
    } catch (error) {
      console.error('Error saving auth state:', error);
      return false;
    }
  }

  /**
   * Clear authentication state
   */
  async clearAuthState() {
    this.isAuthenticated = false;
    this.accessToken = null;
    this.refreshToken = null;
    this.tokenExpiry = null;
    this.userInfo = null;

    try {
      await chrome.storage.local.remove([
        'google_access_token',
        'google_refresh_token',
        'google_token_expiry', 
        'google_user_info',
        'google_auth_enabled'
      ]);
    } catch (error) {
      console.error('Error clearing auth state:', error);
    }
  }

  /**
   * Check if the current access token is expired
   */
  isTokenExpired() {
    if (!this.tokenExpiry) return true;
    return Date.now() >= this.tokenExpiry;
  }

  /**
   * Sign in with Google using Chrome Identity API
   */
  async signIn() {
    try {
      // Use Chrome Identity API's launchWebAuthFlow method
      const clientId = '*************-35d9q9r35e0iqf859419gtqa63ikmm8c.apps.googleusercontent.com'; // Your client ID
      const redirectURL = chrome.identity.getRedirectURL();
      const scopes = 'https://www.googleapis.com/auth/calendar.readonly';
      
      // Build the authorization URL
      const authURL = new URL('https://accounts.google.com/o/oauth2/auth');
      authURL.searchParams.set('client_id', clientId);
      authURL.searchParams.set('response_type', 'token');
      authURL.searchParams.set('redirect_uri', redirectURL);
      authURL.searchParams.set('scope', scopes);
      
      console.log('Using auth URL:', authURL.toString());
      console.log('Redirect URL:', redirectURL);
      
      // Launch the web auth flow
      const responseURL = await chrome.identity.launchWebAuthFlow({
        url: authURL.toString(),
        interactive: true
      });
      
      if (responseURL) {
        // Extract access token from the response URL
        const url = new URL(responseURL);
        const params = new URLSearchParams(url.hash.substring(1));
        this.accessToken = params.get('access_token');
        const expiresIn = params.get('expires_in');
        
        if (this.accessToken) {
          this.isAuthenticated = true;
          this.tokenExpiry = Date.now() + (parseInt(expiresIn) * 1000);
          
          // Get user info
          await this.fetchUserInfo();
          
          // Save state
          await this.saveAuthState();
          
          console.log('Google sign-in successful');
          return true;
        }
      }
    } catch (error) {
      console.error('Google sign-in failed:', error);
      this.clearAuthState();
      throw new Error(`Authentication failed: ${error.message}`);
    }
    
    return false;
  }

  /**
   * Sign out from Google
   */
  async signOut() {
    try {
      if (this.accessToken) {
        // Revoke the token
        await chrome.identity.removeCachedAuthToken({ token: this.accessToken });
      }
      
      await this.clearAuthState();
      console.log('Google sign-out successful');
      return true;
    } catch (error) {
      console.error('Google sign-out failed:', error);
      // Clear state anyway
      await this.clearAuthState();
      return false;
    }
  }

  /**
   * Refresh the access token
   */
  async refreshAccessToken() {
    try {
      // For the launchWebAuthFlow approach, we need to re-authenticate
      // as refresh tokens aren't typically used with this flow
      const clientId = '*************-35d9q9r35e0iqf859419gtqa63ikmm8c.apps.googleusercontent.com';
      const redirectURL = chrome.identity.getRedirectURL();
      const scopes = 'https://www.googleapis.com/auth/calendar.readonly';
      
      const authURL = new URL('https://accounts.google.com/o/oauth2/auth');
      authURL.searchParams.set('client_id', clientId);
      authURL.searchParams.set('response_type', 'token');
      authURL.searchParams.set('redirect_uri', redirectURL);
      authURL.searchParams.set('scope', scopes);
      
      // Non-interactive refresh attempt
      try {
        const responseURL = await chrome.identity.launchWebAuthFlow({
          url: authURL.toString(),
          interactive: false
        });
        
        if (responseURL) {
          const url = new URL(responseURL);
          const params = new URLSearchParams(url.hash.substring(1));
          this.accessToken = params.get('access_token');
          const expiresIn = params.get('expires_in');
          
          if (this.accessToken) {
            this.tokenExpiry = Date.now() + (parseInt(expiresIn) * 1000);
            await this.saveAuthState();
            return true;
          }
        }
      } catch (nonInteractiveError) {
        console.log('Non-interactive token refresh failed, will try interactive');
        // Fall through to interactive auth
      }
      
      // If non-interactive refresh fails, try interactive
      return await this.signIn();
    } catch (error) {
      console.error('Token refresh failed:', error);
      this.clearAuthState();
      return false;
    }
  }

  /**
   * Fetch user information from Google API
   */
  async fetchUserInfo() {
    if (!this.accessToken) return null;

    try {
      const response = await fetch('https://www.googleapis.com/oauth2/v2/userinfo', {
        headers: {
          'Authorization': `Bearer ${this.accessToken}`
        }
      });

      if (response.ok) {
        this.userInfo = await response.json();
        return this.userInfo;
      }
    } catch (error) {
      console.error('Failed to fetch user info:', error);
    }
    
    return null;
  }

  /**
   * Get a valid access token, refreshing if necessary
   */
  async getValidToken() {
    if (!this.isAuthenticated || !this.accessToken) {
      throw new Error('Not authenticated');
    }

    if (this.isTokenExpired()) {
      const refreshed = await this.refreshAccessToken();
      if (!refreshed) {
        throw new Error('Failed to refresh token');
      }
    }

    return this.accessToken;
  }

  /**
   * Make an authenticated API request to Google
   */
  async makeAuthenticatedRequest(url, options = {}) {
    const token = await this.getValidToken();
    
    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
      ...options.headers
    };

    const response = await fetch(url, {
      ...options,
      headers
    });

    if (response.status === 401) {
      // Token might be invalid, try to refresh
      const refreshed = await this.refreshAccessToken();
      if (refreshed) {
        // Retry the request with new token
        const newToken = await this.getValidToken();
        headers['Authorization'] = `Bearer ${newToken}`;
        return fetch(url, { ...options, headers });
      } else {
        throw new Error('Authentication failed');
      }
    }

    return response;
  }

  /**
   * Get authentication status
   */
  getAuthStatus() {
    return {
      isAuthenticated: this.isAuthenticated,
      userInfo: this.userInfo,
      tokenExpiry: this.tokenExpiry,
      isTokenExpired: this.isTokenExpired()
    };
  }
  
  /**
   * Dispatch an event to notify the application that the auth state has changed
   * @private
   */
  _dispatchAuthStateChangedEvent() {
    const event = new CustomEvent('googleAuthStateChanged', {
      detail: this.getAuthStatus()
    });
    window.dispatchEvent(event);
    console.log('Auth state change event dispatched', this.getAuthStatus());
  }
}

// Export for use in other modules
window.GoogleAuthService = GoogleAuthService;
