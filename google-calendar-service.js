/**
 * Google Calendar Service for CalenTask Chrome Extension
 * Handles Google Calendar API interactions
 */
class GoogleCalendarService {
  constructor(authService) {
    this.authService = authService;
    this.baseUrl = 'https://www.googleapis.com/calendar/v3';
    this.calendars = [];
    this.events = [];
    this.lastSyncTime = null;
    this.syncInterval = null;
    this.isEnabled = false;
    
    // Load settings from storage
    this.loadSettings();
  }

  /**
   * Load service settings from Chrome storage
   */
  async loadSettings() {
    try {
      const result = await chrome.storage.local.get([
        'google_calendar_enabled',
        'google_calendar_sync_interval',
        'google_calendar_last_sync',
        'google_calendar_events'
      ]);

      this.isEnabled = result.google_calendar_enabled || false;
      this.lastSyncTime = result.google_calendar_last_sync || null;
      this.events = result.google_calendar_events || [];
      
      const syncInterval = result.google_calendar_sync_interval || 15; // Default 15 minutes
      this.setupAutoSync(syncInterval);
    } catch (error) {
      console.error('Error loading calendar settings:', error);
    }
  }

  /**
   * Save service settings to Chrome storage
   */
  async saveSettings() {
    try {
      await chrome.storage.local.set({
        google_calendar_enabled: this.isEnabled,
        google_calendar_last_sync: this.lastSyncTime,
        google_calendar_events: this.events
      });
    } catch (error) {
      console.error('Error saving calendar settings:', error);
    }
  }

  /**
   * Enable Google Calendar integration
   */
  async enable() {
    if (!this.authService.isAuthenticated) {
      throw new Error('Must be authenticated to enable Google Calendar');
    }

    this.isEnabled = true;
    await this.saveSettings();
    await this.syncCalendarEvents();
    this.setupAutoSync(15); // Sync every 15 minutes
  }

  /**
   * Disable Google Calendar integration
   */
  async disable() {
    this.isEnabled = false;
    this.events = [];
    this.clearAutoSync();
    await this.saveSettings();
  }

  /**
   * Setup automatic syncing
   */
  setupAutoSync(intervalMinutes = 15) {
    this.clearAutoSync();
    
    if (this.isEnabled && this.authService.isAuthenticated) {
      this.syncInterval = setInterval(() => {
        this.syncCalendarEvents().catch(error => {
          console.error('Auto-sync failed:', error);
        });
      }, intervalMinutes * 60 * 1000);
    }
  }

  /**
   * Clear automatic syncing
   */
  clearAutoSync() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
  }

  /**
   * Get list of user's calendars
   */
  async getCalendarList() {
    if (!this.authService.isAuthenticated) {
      throw new Error('Not authenticated');
    }

    try {
      const response = await this.authService.makeAuthenticatedRequest(
        `${this.baseUrl}/users/me/calendarList`
      );

      if (response.ok) {
        const data = await response.json();
        this.calendars = data.items || [];
        return this.calendars;
      } else {
        throw new Error(`Failed to fetch calendars: ${response.status}`);
      }
    } catch (error) {
      console.error('Error fetching calendar list:', error);
      throw error;
    }
  }

  /**
   * Get events from user's primary calendar
   */
  async getCalendarEvents(calendarId = 'primary', timeMin = null, timeMax = null) {
    if (!this.authService.isAuthenticated) {
      throw new Error('Not authenticated');
    }

    // Default to current week if no time range specified
    if (!timeMin) {
      const now = new Date();
      const weekStart = new Date(now);
      weekStart.setDate(now.getDate() - now.getDay()); // Start of week (Sunday)
      weekStart.setHours(0, 0, 0, 0);
      timeMin = weekStart.toISOString();
    }

    if (!timeMax) {
      const now = new Date();
      const weekEnd = new Date(now);
      weekEnd.setDate(now.getDate() - now.getDay() + 6); // End of week (Saturday)
      weekEnd.setHours(23, 59, 59, 999);
      timeMax = weekEnd.toISOString();
    }

    try {
      const params = new URLSearchParams({
        timeMin: timeMin,
        timeMax: timeMax,
        singleEvents: 'true',
        orderBy: 'startTime',
        maxResults: '250'
      });

      const response = await this.authService.makeAuthenticatedRequest(
        `${this.baseUrl}/calendars/${encodeURIComponent(calendarId)}/events?${params}`
      );

      if (response.ok) {
        const data = await response.json();
        return data.items || [];
      } else {
        throw new Error(`Failed to fetch events: ${response.status}`);
      }
    } catch (error) {
      console.error('Error fetching calendar events:', error);
      throw error;
    }
  }

  /**
   * Sync calendar events from Google Calendar
   */
  async syncCalendarEvents() {
    if (!this.isEnabled || !this.authService.isAuthenticated) {
      return;
    }

    try {
      console.log('Syncing Google Calendar events...');
      
      // Get events for the current week and next week
      const now = new Date();
      const weekStart = new Date(now);
      weekStart.setDate(now.getDate() - now.getDay() - 7); // Start from last week
      weekStart.setHours(0, 0, 0, 0);
      
      const weekEnd = new Date(now);
      weekEnd.setDate(now.getDate() - now.getDay() + 14); // End at next week
      weekEnd.setHours(23, 59, 59, 999);

      const events = await this.getCalendarEvents(
        'primary',
        weekStart.toISOString(),
        weekEnd.toISOString()
      );

      // Transform Google Calendar events to our format
      this.events = events.map(event => this.transformGoogleEvent(event));
      
      this.lastSyncTime = new Date().toISOString();
      await this.saveSettings();
      
      console.log(`Synced ${this.events.length} Google Calendar events`);
      
      // Notify listeners about the sync
      this.notifyEventUpdate();
      
      return this.events;
    } catch (error) {
      console.error('Failed to sync calendar events:', error);
      throw error;
    }
  }

  /**
   * Transform Google Calendar event to our internal format
   */
  transformGoogleEvent(googleEvent) {
    const isAllDay = !googleEvent.start.dateTime;
    
    let startDate, endDate;
    
    if (isAllDay) {
      // All-day events use date instead of dateTime
      startDate = new Date(googleEvent.start.date + 'T00:00:00');
      endDate = googleEvent.end.date ? new Date(googleEvent.end.date + 'T00:00:00') : startDate;
    } else {
      startDate = new Date(googleEvent.start.dateTime);
      endDate = googleEvent.end.dateTime ? new Date(googleEvent.end.dateTime) : startDate;
    }

    return {
      id: `google_${googleEvent.id}`,
      title: googleEvent.summary || 'Untitled Event',
      description: googleEvent.description || '',
      date: startDate,
      endTime: isAllDay ? null : endDate,
      isFullDay: isAllDay,
      isGoogleEvent: true,
      googleEventId: googleEvent.id,
      location: googleEvent.location || '',
      attendees: googleEvent.attendees || [],
      htmlLink: googleEvent.htmlLink || '',
      status: googleEvent.status || 'confirmed',
      created: googleEvent.created ? new Date(googleEvent.created) : null,
      updated: googleEvent.updated ? new Date(googleEvent.updated) : null
    };
  }

  /**
   * Get all synced Google Calendar events
   */
  getEvents() {
    return this.events;
  }

  /**
   * Get events for a specific date range
   */
  getEventsInRange(startDate, endDate) {
    return this.events.filter(event => {
      const eventDate = new Date(event.date);
      return eventDate >= startDate && eventDate <= endDate;
    });
  }

  /**
   * Check if service is enabled and authenticated
   */
  isReady() {
    return this.isEnabled && this.authService.isAuthenticated;
  }

  /**
   * Get sync status information
   */
  getSyncStatus() {
    return {
      isEnabled: this.isEnabled,
      isAuthenticated: this.authService.isAuthenticated,
      lastSyncTime: this.lastSyncTime,
      eventCount: this.events.length,
      isReady: this.isReady()
    };
  }

  /**
   * Notify listeners about event updates
   */
  notifyEventUpdate() {
    // Dispatch custom event for calendar updates
    const event = new CustomEvent('googleCalendarEventsUpdated', {
      detail: {
        events: this.events,
        syncTime: this.lastSyncTime
      }
    });
    window.dispatchEvent(event);
  }

  /**
   * Force a manual sync
   */
  async forcSync() {
    if (!this.isReady()) {
      throw new Error('Google Calendar service not ready');
    }
    
    return await this.syncCalendarEvents();
  }
}

// Export for use in other modules
window.GoogleCalendarService = GoogleCalendarService;
